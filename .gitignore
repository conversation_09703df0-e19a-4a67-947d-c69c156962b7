# See http://help.github.com/ignore-files/ for more about ignoring files.

# Compiled output
/dist
/tmp
/out-tsc

# Dependencies
/node_modules

# Cordova
/www
/plugins
/platforms

# IDEs and editors
.idea/*
!.idea/runConfigurations/
!.idea/codeStyleSettings.xml
.project
.classpath
.c9/
*.launch
.settings/
xcuserdata/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Maven
/target
/log

# Misc
/.sass-cache
/connect.lock
/coverage/*
/libpeerconnection.log
npm-debug.log
testem.log
/typings
/reports
/src/translations/template.*
/src/environments/.env.json

# E2E
/e2e/*.js
/e2e/*.map

# System Files
.DS_Store
Thumbs.db
