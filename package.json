{"name": "ever-hair", "version": "2.0.0", "private": true, "scripts": {"ng": "ng", "build": "npm run env -s && ng build --prod", "start": "ng serve --proxy-config proxy.conf.js", "lint": "ng lint --type-check && stylelint \"src/**/*.scss\" --syntax scss && htmlhint \"src\" --config .htmlhintrc", "test": "ng test", "test:ci": "npm run lint -s && ng test --single-run --code-coverage", "e2e": "ng e2e", "translations:extract": "ngx-translate-extract --input ./src --output ./src/translations/template.json --format=json --clean -sort --marker extract", "docs": "hads ./docs -o", "env": "ngx-scripts env2json npm_package_version", "prepare": "ngx-scripts unpin-ionic-dependencies", "cordova:prepare": "ngx-scripts cordova prepare", "cordova:run": "ngx-scripts cordova run --dev", "cordova:build": "ngx-scripts cordova build --device --release --copy dist", "cordova:clean": "ngx-scripts clean", "cordova": "<PERSON><PERSON>", "generate": "ng generate"}, "dependencies": {"@angular/animations": "^4.4.7", "@angular/common": "^4.4.7", "@angular/compiler": "^4.4.7", "@angular/core": "^4.4.7", "@angular/forms": "^4.4.7", "@angular/http": "^4.4.7", "@angular/platform-browser": "^4.4.7", "@angular/platform-browser-dynamic": "^4.4.7", "@angular/router": "^4.4.7", "@angular/service-worker": "^1.0.0-beta.16", "@ionic-native/call-number": "4.3.3", "@ionic-native/camera": "^3.14.0", "@ionic-native/core": "4.0.0", "@ionic-native/firebase": "^3.14.0", "@ionic-native/in-app-browser": "^3.14.0", "@ionic-native/keyboard": "4.0.0", "@ionic-native/native-storage": "3.1.0-rc.1", "@ionic-native/screen-orientation": "4.3.2", "@ionic-native/splash-screen": "4.0.0", "@ionic-native/status-bar": "4.0.0", "@ionic/app-scripts": "^3.2.0", "@ionic/storage": "2.1.3", "@ngx-translate/core": "^8.0.0", "android-versions": "^1.7.0", "angularfire2": "5.0.0-rc.3", "call-number": "^1.0.1", "cordova": "^9.0.0", "cordova-android": "^9.1.0", "cordova-android-support-gradle-release": "^3.0.1", "cordova-ios": "^5.0.0", "cordova-plugin-androidx": "^2.0.0", "cordova-plugin-androidx-adapter": "^1.1.1", "cordova-plugin-camera": "^5.0.3", "cordova-plugin-device": "^2.0.3", "cordova-plugin-firebasex": "^9.1.1", "cordova-plugin-inappbrowser": "^3.2.0", "cordova-plugin-screen-orientation": "^2.0.2", "cordova-plugin-splashscreen": "^4.1.0", "cordova-plugin-statusbar": "^2.4.2", "cordova-plugin-whitelist": "^1.3.3", "cordova-plugin-wkwebview-engine": "git+https://github.com/ionic-team/cordova-plugin-wkwebview-engine.git", "core-js": "^2.6.12", "es6-promise-plugin": "^4.2.2", "firebase": "^4.8.0", "intl": "^1.2.5", "ionic": "^3.20.1", "ionic-angular": "^3.9.10", "ionic-plugin-keyboard": "^2.2.1", "ionic2-calendar": "git+https://gitlab.com/teorone94/ionic2-calendar.git", "ionicons": "^3.0.0", "lodash": "^4.17.21", "rxjs": "^5.5.12", "ts-md5": "^1.3.1", "zone.js": "^0.8.29"}, "devDependencies": {"@angular/cli": "^1.7.4", "@angular/compiler-cli": "^4.4.7", "@angular/language-service": "^4.4.7", "@biesbjerg/ngx-translate-extract": "^2.3.4", "@ngx-rocket/scripts": "^1.1.0", "@types/jasmine": "^2.8.19", "@types/jasminewd2": "^2.0.10", "@types/lodash": "^4.14.112", "@types/node": "^12.20.0", "codelyzer": "~3.2.2", "hads": "^1.7.3", "htmlhint": "^1.1.4", "https-proxy-agent": "^5.0.1", "node-sass": "^4.14.1", "phantomjs-prebuilt": "^2.1.16", "protractor": "^5.4.4", "stylelint": "~8.4.0", "stylelint-config-recommended-scss": "~2.0.0", "stylelint-config-standard": "~17.0.0", "stylelint-scss": "~2.5.0", "ts-node": "^3.3.0", "tslint": "~5.20.1", "typescript": "^2.3.4"}, "cordova": {"platforms": ["ios", "android"], "plugins": {"cordova-plugin-device": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-statusbar": {}, "cordova-plugin-whitelist": {}, "cordova-plugin-wkwebview-engine": {}, "ionic-plugin-keyboard": {}, "cordova-plugin-screen-orientation": {}, "call-number": {}, "mx.ferreyra.callnumber": {}, "cordova-android-support-gradle-release": {"ANDROID_SUPPORT_VERSION": "27.+"}, "cordova-plugin-firebasex": {"FIREBASE_ANALYTICS_COLLECTION_ENABLED": "true", "FIREBASE_PERFORMANCE_COLLECTION_ENABLED": "true", "FIREBASE_CRASHLYTICS_COLLECTION_ENABLED": "true", "ANDROID_ICON_ACCENT": "#FF00FFFF", "ANDROID_PLAY_SERVICES_TAGMANAGER_VERSION": "17.0.0", "ANDROID_PLAY_SERVICES_AUTH_VERSION": "18.0.0", "ANDROID_FIREBASE_ANALYTICS_VERSION": "17.4.0", "ANDROID_FIREBASE_MESSAGING_VERSION": "20.1.6", "ANDROID_FIREBASE_CONFIG_VERSION": "19.1.4", "ANDROID_FIREBASE_PERF_VERSION": "19.0.7", "ANDROID_FIREBASE_AUTH_VERSION": "19.3.1", "ANDROID_FIREBASE_INAPPMESSAGING_VERSION": "19.0.6", "ANDROID_FIREBASE_FIRESTORE_VERSION": "21.4.3", "ANDROID_CRASHLYTICS_VERSION": "2.10.1", "ANDROID_CRASHLYTICS_NDK_VERSION": "2.1.1", "ANDROID_GSON_VERSION": "2.8.6"}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-telerik-imagepicker": {"PHOTO_LIBRARY_USAGE_DESCRIPTION": "Carica immagini", "ANDROID_SUPPORT_V7_VERSION": "27.+"}, "cordova-plugin-camera": {"ANDROID_SUPPORT_V4_VERSION": "27.+"}}}}