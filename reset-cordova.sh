#!/bin/sh

#rm -rf node_modules/;
#npm cache clean --force;
#rm package-lock.json;
#npm i;
#npm i -D @types/lodash@ts2.3;
#npm install --save-dev  --unsafe-perm node-sass;
#npm  i firebase@4.8.0;
#
#npm start;

rm -rf platforms/;
rm -rf plugins/;
npm run cordova:clean;
mkdir www;

npm run cordova platform add android;
npm run cordova:prepare android;
#npm run cordova:build
npm run cordova:run android --device;
#npm run cordova platform add ios;
# shellcheck disable=SC2164
#cd platform/ios;
#pod install;
#npm run cordova:prepare ios;

#npm run cordova:run android;


