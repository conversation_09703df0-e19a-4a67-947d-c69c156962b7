<?xml version='1.0' encoding='utf-8'?>
<widget id="grapyfox.barber.everhair.app" version="2.0.1" xmlns="http://www.w3.org/ns/widgets">
    <name>EverHair</name>
    <description>A mobile app for barber's bookings.</description>
    <author email="<EMAIL>" href="">GrapyFox Co</author>
    <content src="index.html" />
    <access origin="*" />
    <allow-navigation href="http://localhost:8080/*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:<EMAIL>" />
    <allow-intent href="geo:*" />
    <platform name="android">
        <allow-intent href="market:*" />
        <preference name="android-minSdkVersion" value="24" />
        <preference name="android-targetSdkVersion" value="34" />
        <preference name="android-compileSdkVersion" value="34" />
        <preference name="android-buildToolsVersion" value="34.0.0" />
        <preference name="GradlePluginVersion" value="8.2.1" />
        <preference name="AndroidXEnabled" value="true" />
        <preference name="AndroidPersistentFileLocation" value="Compatibility" />
        <preference name="KeyboardDisplayRequiresUserAction" value="false" />
        <preference name="android-manifest/application/activity/@android:theme" value="@android:style/Theme.DeviceDefault" />
        <hook src="node_modules/@ngx-rocket/scripts/hooks/after_prepare/copy-android-resources.js" type="after_prepare" />
        <icon density="ldpi" src="resources/android/icon/drawable-ldpi-icon.png" />
        <icon density="mdpi" src="resources/android/icon/drawable-mdpi-icon.png" />
        <icon density="hdpi" src="resources/android/icon/drawable-hdpi-icon.png" />
        <icon density="xhdpi" src="resources/android/icon/drawable-xhdpi-icon.png" />
        <icon density="xxhdpi" src="resources/android/icon/drawable-xxhdpi-icon.png" />
        <icon density="xxxhdpi" src="resources/android/icon/drawable-xxxhdpi-icon.png" />
        <splash density="land-ldpi" src="resources/android/splash/drawable-land-ldpi-screen.png" />
        <splash density="land-mdpi" src="resources/android/splash/drawable-land-mdpi-screen.png" />
        <splash density="land-hdpi" src="resources/android/splash/drawable-land-hdpi-screen.png" />
        <splash density="land-xhdpi" src="resources/android/splash/drawable-land-xhdpi-screen.png" />
        <splash density="land-xxhdpi" src="resources/android/splash/drawable-land-xxhdpi-screen.png" />
        <splash density="land-xxxhdpi" src="resources/android/splash/drawable-land-xxxhdpi-screen.png" />
        <splash density="port-ldpi" src="resources/android/splash/drawable-port-ldpi-screen.png" />
        <splash density="port-mdpi" src="resources/android/splash/drawable-port-mdpi-screen.png" />
        <splash density="port-hdpi" src="resources/android/splash/drawable-port-hdpi-screen.png" />
        <splash density="port-xhdpi" src="resources/android/splash/drawable-port-xhdpi-screen.png" />
        <splash density="port-xxhdpi" src="resources/android/splash/drawable-port-xxhdpi-screen.png" />
        <splash density="port-xxxhdpi" src="resources/android/splash/drawable-port-xxxhdpi-screen.png" />
    </platform>
    <platform name="ios">
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <preference name="deployment-target" value="15.0" />
        <preference name="WKWebViewOnly" value="true" />
        <preference name="BackupWebStorage" value="none" />
        <preference name="CordovaWebViewEngine" value="CDVWKWebViewEngine" />
        <preference name="WKSuspendInBackground" value="false" />
        <preference name="WKWebViewDecelerationSpeed" value="normal" />
        <config-file mode="replace" parent="UIStatusBarStyle" platform="ios" target="*-Info.plist">
            <string>UIStatusBarStyleLightContent</string>
        </config-file>
      <config-file parent="NSCameraUsageDescription" platform="ios" target="*-Info.plist">
        <string>Immagini per rewards</string>
      </config-file>
        <config-file mode="replace" parent="UISupportedInterfaceOrientations~ipad" platform="ios" target="*-Info.plist">
            <array>
                <string>UIInterfaceOrientationPortrait</string>
                <string>UIInterfaceOrientationPortraitUpsideDown</string>
                <string>UIInterfaceOrientationLandscapeLeft</string>
                <string>UIInterfaceOrientationLandscapeRight</string>
            </array>
        </config-file>
        <icon height="57" src="resources/ios/icon/icon.png" width="57" />
        <icon height="114" src="resources/ios/icon/<EMAIL>" width="114" />
        <icon height="40" src="resources/ios/icon/icon-40.png" width="40" />
        <icon height="80" src="resources/ios/icon/<EMAIL>" width="80" />
        <icon height="120" src="resources/ios/icon/<EMAIL>" width="120" />
        <icon height="50" src="resources/ios/icon/icon-50.png" width="50" />
        <icon height="100" src="resources/ios/icon/<EMAIL>" width="100" />
        <icon height="60" src="resources/ios/icon/icon-60.png" width="60" />
        <icon height="120" src="resources/ios/icon/<EMAIL>" width="120" />
        <icon height="180" src="resources/ios/icon/<EMAIL>" width="180" />
        <icon height="72" src="resources/ios/icon/icon-72.png" width="72" />
        <icon height="144" src="resources/ios/icon/<EMAIL>" width="144" />
        <icon height="76" src="resources/ios/icon/icon-76.png" width="76" />
        <icon height="152" src="resources/ios/icon/<EMAIL>" width="152" />
        <icon height="167" src="resources/ios/icon/<EMAIL>" width="167" />
        <splash height="2732" src="resources/ios/splash/Default@2x~universal~anyany.png" width="2732" />
        <icon height="20" src="resources/ios/icon/icon-20.png" width="20" />
        <icon height="40" src="resources/ios/icon/<EMAIL>" width="40" />
        <icon height="60" src="resources/ios/icon/<EMAIL>" width="60" />
        <icon height="29" src="resources/ios/icon/icon-29.png" width="29" />
        <icon height="58" src="resources/ios/icon/<EMAIL>" width="58" />
        <icon height="87" src="resources/ios/icon/<EMAIL>" width="87" />
        <icon height="48" src="resources/ios/icon/<EMAIL>" width="48" />
        <icon height="55" src="resources/ios/icon/<EMAIL>" width="55" />
        <icon height="88" src="resources/ios/icon/<EMAIL>" width="88" />
        <icon height="172" src="resources/ios/icon/<EMAIL>" width="172" />
        <icon height="196" src="resources/ios/icon/<EMAIL>" width="196" />
        <icon height="216" src="resources/ios/icon/<EMAIL>" width="216" />
        <icon height="1024" src="resources/ios/icon/icon-1024.png" width="1024" />
        <splash height="1136" src="resources/ios/splash/Default-568h@2x~iphone.png" width="640" />
        <splash height="1334" src="resources/ios/splash/Default-667h.png" width="750" />
        <splash height="2688" src="resources/ios/splash/Default-2688h~iphone.png" width="1242" />
        <splash height="1792" src="resources/ios/splash/Default-1792h~iphone.png" width="828" />
        <splash height="2436" src="resources/ios/splash/Default-2436h.png" width="1125" />
        <splash height="2208" src="resources/ios/splash/Default-736h.png" width="1242" />
        <splash height="2048" src="resources/ios/splash/Default-Portrait@2x~ipad.png" width="1536" />
        <splash height="2732" src="resources/ios/splash/Default-Portrait@~ipadpro.png" width="2048" />
        <splash height="1024" src="resources/ios/splash/Default-Portrait~ipad.png" width="768" />
        <splash height="960" src="resources/ios/splash/Default@2x~iphone.png" width="640" />
        <splash height="480" src="resources/ios/splash/Default~iphone.png" width="320" />
    </platform>
    <feature name="StatusBar">
        <param name="ios-package" onload="true" value="CDVStatusBar" />
    </feature>
    <feature name="CDVWKWebViewEngine">
        <param name="ios-package" value="CDVWKWebViewEngine" />
    </feature>
    <preference name="Fullscreen" value="false" />
    <preference name="DisallowOverscroll" value="true" />
    <preference name="Orientation" value="portrait" />
    <preference name="StatusBarStyle" value="lightcontent" />
    <preference name="AutoHideSplashScreen" value="false" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="FadeSplashScreenDuration" value="500" />
    <preference name="SplashShowOnlyFirstTime" value="true" />
    <plugin name="cordova-plugin-firebasex" spec="^16.5.0" />
    <plugin name="cordova-plugin-device" spec="^3.0.0" />
    <plugin name="cordova-plugin-splashscreen" spec="^6.0.2" />
    <plugin name="cordova-plugin-statusbar" spec="^4.0.0" />
    <plugin name="cordova-plugin-whitelist" spec="^1.3.5" />
    <plugin name="ionic-plugin-keyboard" spec="^2.2.1" />
    <plugin name="cordova-plugin-screen-orientation" spec="^3.0.4" />
    <plugin name="mx.ferreyra.callnumber" spec="^1.0.1" />
    <plugin name="cordova-android-support-gradle-release" spec="^3.0.1">
        <variable name="ANDROID_SUPPORT_VERSION" value="27.+" />
    </plugin>
    <plugin name="cordova-plugin-wkwebview-engine" spec="~1.1.6" />
</widget>
