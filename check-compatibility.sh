#!/bin/bash

# Script per verificare la compatibilità del progetto EverHair

echo "🔍 Verifica Compatibilità EverHair"
echo "=================================="

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funzione per check
check_requirement() {
    local name="$1"
    local command="$2"
    local expected="$3"
    
    echo -n "Checking $name... "
    
    if eval "$command" &>/dev/null; then
        echo -e "${GREEN}✓${NC}"
        return 0
    else
        echo -e "${RED}✗${NC} (Expected: $expected)"
        return 1
    fi
}

# Verifica Node.js
echo "📦 Verifica Node.js e npm"
node_version=$(node --version 2>/dev/null | cut -d'v' -f2)
if [[ "$node_version" ]]; then
    major_version=$(echo $node_version | cut -d'.' -f1)
    if [[ $major_version -ge 16 ]]; then
        echo -e "Node.js: ${GREEN}✓${NC} v$node_version"
    else
        echo -e "Node.js: ${YELLOW}⚠${NC} v$node_version (Raccomandato: 16+)"
    fi
else
    echo -e "Node.js: ${RED}✗${NC} Non installato"
fi

npm_version=$(npm --version 2>/dev/null)
if [[ "$npm_version" ]]; then
    echo -e "npm: ${GREEN}✓${NC} v$npm_version"
else
    echo -e "npm: ${RED}✗${NC} Non installato"
fi

echo ""

# Verifica Android
echo "🤖 Verifica Ambiente Android"
if [[ "$ANDROID_HOME" ]]; then
    echo -e "ANDROID_HOME: ${GREEN}✓${NC} $ANDROID_HOME"
    
    # Verifica SDK 34
    if [[ -d "$ANDROID_HOME/platforms/android-34" ]]; then
        echo -e "Android SDK 34: ${GREEN}✓${NC}"
    else
        echo -e "Android SDK 34: ${RED}✗${NC} Non installato"
    fi
    
    # Verifica Build Tools 34.0.0
    if [[ -d "$ANDROID_HOME/build-tools/34.0.0" ]]; then
        echo -e "Build Tools 34.0.0: ${GREEN}✓${NC}"
    else
        echo -e "Build Tools 34.0.0: ${RED}✗${NC} Non installato"
    fi
    
else
    echo -e "ANDROID_HOME: ${RED}✗${NC} Non configurato"
fi

# Verifica Java
java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
if [[ "$java_version" ]]; then
    echo -e "Java: ${GREEN}✓${NC} $java_version"
else
    echo -e "Java: ${RED}✗${NC} Non installato"
fi

echo ""

# Verifica iOS (solo su macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Verifica Ambiente iOS"
    
    # Verifica Xcode
    if command -v xcodebuild &> /dev/null; then
        xcode_version=$(xcodebuild -version | head -n 1 | cut -d' ' -f2)
        echo -e "Xcode: ${GREEN}✓${NC} $xcode_version"
        
        # Verifica iOS SDK
        ios_sdk=$(xcodebuild -showsdks | grep iphoneos | tail -n 1 | cut -d' ' -f4)
        if [[ "$ios_sdk" ]]; then
            echo -e "iOS SDK: ${GREEN}✓${NC} $ios_sdk"
        fi
    else
        echo -e "Xcode: ${RED}✗${NC} Non installato"
    fi
    
    # Verifica CocoaPods
    if command -v pod &> /dev/null; then
        pod_version=$(pod --version)
        echo -e "CocoaPods: ${GREEN}✓${NC} v$pod_version"
    else
        echo -e "CocoaPods: ${YELLOW}⚠${NC} Non installato (opzionale)"
    fi
else
    echo "🍎 Ambiente iOS: Non disponibile (richiede macOS)"
fi

echo ""

# Verifica configurazione progetto
echo "⚙️ Verifica Configurazione Progetto"

# Verifica package.json
if [[ -f "package.json" ]]; then
    echo -e "package.json: ${GREEN}✓${NC}"
    
    # Verifica versioni Cordova
    cordova_version=$(grep '"cordova"' package.json | cut -d'"' -f4)
    if [[ "$cordova_version" ]]; then
        echo -e "Cordova: ${GREEN}✓${NC} $cordova_version"
    fi
    
    cordova_android=$(grep '"cordova-android"' package.json | cut -d'"' -f4)
    if [[ "$cordova_android" ]]; then
        echo -e "Cordova Android: ${GREEN}✓${NC} $cordova_android"
    fi
    
    cordova_ios=$(grep '"cordova-ios"' package.json | cut -d'"' -f4)
    if [[ "$cordova_ios" ]]; then
        echo -e "Cordova iOS: ${GREEN}✓${NC} $cordova_ios"
    fi
else
    echo -e "package.json: ${RED}✗${NC} Non trovato"
fi

# Verifica config.xml
if [[ -f "config.xml" ]]; then
    echo -e "config.xml: ${GREEN}✓${NC}"
    
    # Verifica target SDK Android
    target_sdk=$(grep 'android-targetSdkVersion' config.xml | cut -d'"' -f4)
    if [[ "$target_sdk" == "34" ]]; then
        echo -e "Android Target SDK: ${GREEN}✓${NC} $target_sdk"
    else
        echo -e "Android Target SDK: ${YELLOW}⚠${NC} $target_sdk (Raccomandato: 34)"
    fi
    
    # Verifica deployment target iOS
    ios_target=$(grep 'deployment-target' config.xml | cut -d'"' -f4)
    if [[ "$ios_target" ]]; then
        echo -e "iOS Deployment Target: ${GREEN}✓${NC} $ios_target"
    fi
else
    echo -e "config.xml: ${RED}✗${NC} Non trovato"
fi

echo ""

# Verifica dipendenze
echo "📋 Verifica Dipendenze"
if [[ -d "node_modules" ]]; then
    echo -e "node_modules: ${GREEN}✓${NC} Installato"
else
    echo -e "node_modules: ${YELLOW}⚠${NC} Non installato (eseguire npm install)"
fi

if [[ -d "platforms" ]]; then
    echo -e "platforms: ${GREEN}✓${NC} Configurato"
    
    if [[ -d "platforms/android" ]]; then
        echo -e "  - Android: ${GREEN}✓${NC}"
    fi
    
    if [[ -d "platforms/ios" ]]; then
        echo -e "  - iOS: ${GREEN}✓${NC}"
    fi
else
    echo -e "platforms: ${YELLOW}⚠${NC} Non configurato (eseguire cordova platform add)"
fi

echo ""
echo "🎯 Raccomandazioni:"
echo "1. Assicurarsi che Android Target SDK sia 34 per Google Play Store"
echo "2. Assicurarsi che iOS Deployment Target sia 15.0+ per App Store"
echo "3. Testare su dispositivi reali prima del deploy"
echo "4. Verificare che tutte le funzionalità Firebase funzionino"
echo ""
echo "📚 Per aggiornamenti: ./update-compatibility.sh"
