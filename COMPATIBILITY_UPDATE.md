# Aggiornamento Compatibilità Android e iOS - EverHair

## Panoramica
Questo documento descrive gli aggiornamenti necessari per rendere l'app EverHair compatibile con le ultime versioni di Android e iOS, rispettando i requisiti del Google Play Store e App Store del 2024.

## Requisiti Aggiornati

### Android
- **Target SDK**: 34 (Android 14) - Obbligatorio per Google Play Store dal 31 agosto 2024
- **Min SDK**: 24 (Android 7.0) - Mantenuto per compatibilità
- **Compile SDK**: 34
- **Build Tools**: 34.0.0
- **Gradle Plugin**: 8.2.1
- **AndroidX**: Abilitato

### iOS
- **Deployment Target**: 15.0 - Ra<PERSON>mandato per compatibilità con le ultime funzionalità
- **Xcode**: 15+ richiesto per build App Store
- **WKWebView**: Obbligatorio (già configurato)

## Aggiornamenti Effettuati

### 1. Package.json
```json
{
  "cordova": "^12.0.0",
  "cordova-android": "^13.0.0",
  "cordova-ios": "^7.1.0",
  "cordova-plugin-firebasex": "^16.5.0",
  "cordova-plugin-device": "^3.0.0",
  "cordova-plugin-camera": "^7.0.0",
  "cordova-plugin-splashscreen": "^6.0.2",
  "cordova-plugin-statusbar": "^4.0.0",
  "cordova-plugin-inappbrowser": "^6.0.0",
  "cordova-plugin-screen-orientation": "^3.0.4",
  "cordova-plugin-androidx": "^3.0.0",
  "core-js": "^3.38.1"
}
```

### 2. Config.xml
```xml
<!-- Android -->
<preference name="android-targetSdkVersion" value="34" />
<preference name="android-compileSdkVersion" value="34" />
<preference name="android-buildToolsVersion" value="34.0.0" />
<preference name="AndroidXEnabled" value="true" />

<!-- iOS -->
<preference name="deployment-target" value="15.0" />
```

### 3. Firebase Versioni Aggiornate
```json
{
  "ANDROID_FIREBASE_ANALYTICS_VERSION": "22.1.2",
  "ANDROID_FIREBASE_MESSAGING_VERSION": "24.0.1",
  "ANDROID_FIREBASE_AUTH_VERSION": "23.0.0",
  "ANDROID_FIREBASE_FIRESTORE_VERSION": "25.1.0"
}
```

## Istruzioni per l'Aggiornamento

### Metodo Automatico
```bash
chmod +x update-compatibility.sh
./update-compatibility.sh
```

### Metodo Manuale

1. **Backup del progetto**
   ```bash
   cp package.json package.json.backup
   cp config.xml config.xml.backup
   ```

2. **Pulizia ambiente**
   ```bash
   rm -rf node_modules platforms plugins
   npm cache clean --force
   ```

3. **Installazione dipendenze**
   ```bash
   npm install
   ```

4. **Aggiunta piattaforme aggiornate**
   ```bash
   npm run cordova platform add android@13.0.0
   npm run cordova platform add ios@7.1.0
   ```

5. **Preparazione build**
   ```bash
   npm run cordova:prepare
   ```

## Test e Verifica

### Android
```bash
# Build di test
npm run cordova:build android

# Verifica target SDK
grep -r "targetSdkVersion" platforms/android/

# Test su dispositivo
npm run cordova:run android --device
```

### iOS
```bash
# Build di test
npm run cordova:build ios

# Verifica deployment target
grep -r "IPHONEOS_DEPLOYMENT_TARGET" platforms/ios/

# Test su simulatore
npm run cordova:run ios --emulator
```

## Problemi Comuni e Soluzioni

### 1. Errore Build Tools Android
```bash
# Se mancano i build tools 34.0.0
cd ~/Library/Android/sdk/build-tools/34.0.0
# Verificare che d8 e dx siano presenti
```

### 2. Errore AndroidX
```bash
# Assicurarsi che AndroidX sia abilitato
echo "android.useAndroidX=true" >> platforms/android/gradle.properties
echo "android.enableJetifier=true" >> platforms/android/gradle.properties
```

### 3. Errore iOS Deployment Target
```bash
# Verificare in platforms/ios/EverHair.xcodeproj/project.pbxproj
# IPHONEOS_DEPLOYMENT_TARGET = 15.0;
```

## Requisiti di Sistema

### Sviluppo Android
- Android Studio Jellyfish (2023.3.1) o superiore
- Android SDK 34
- Build Tools 34.0.0
- Java 17+

### Sviluppo iOS
- macOS Monterey (12.0) o superiore
- Xcode 15.0 o superiore
- iOS SDK 17.0+
- CocoaPods aggiornato

## Note Importanti

1. **Google Play Store**: Apps devono targetare SDK 34+ dal 31 agosto 2024
2. **App Store**: Apps devono essere compilate con Xcode 15+ per iOS 17
3. **Firebase**: Versioni aggiornate per compatibilità con Android 14
4. **AndroidX**: Obbligatorio per le nuove versioni dei plugin
5. **WKWebView**: Già configurato, necessario per iOS

## Prossimi Passi

1. Eseguire l'aggiornamento usando lo script fornito
2. Testare tutte le funzionalità dell'app
3. Verificare la compatibilità con Firebase
4. Testare su dispositivi reali con Android 14 e iOS 17
5. Preparare per il deploy su store

## Supporto

Per problemi durante l'aggiornamento:
1. Verificare i log di build dettagliati
2. Controllare la compatibilità dei plugin
3. Consultare la documentazione Cordova ufficiale
4. Verificare i requisiti di sistema
