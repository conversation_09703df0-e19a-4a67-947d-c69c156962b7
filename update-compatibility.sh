#!/bin/bash

# Script per aggiornare EverHair alla compatibilità con le ultime versioni Android e iOS

echo "🚀 Aggiornamento compatibilità Android e iOS per EverHair"
echo "========================================================="

# Backup del progetto
echo "📦 Creazione backup..."
cp package.json package.json.backup
cp config.xml config.xml.backup

# Pulizia cache e node_modules
echo "🧹 Pulizia cache e dipendenze..."
rm -rf node_modules
rm -rf platforms
rm -rf plugins
npm cache clean --force

# Installazione delle nuove dipendenze
echo "📥 Installazione nuove dipendenze..."
npm install --force

# Aggiornamento Cordova platforms
echo "🔧 Aggiornamento piattaforme Cordova..."
npm run cordova platform add android@13.0.0
npm run cordova platform add ios@7.1.0

# Installazione plugin aggiornati
echo "🔌 Installazione plugin aggiornati..."
npm run cordova plugin add cordova-plugin-device@3.0.0
npm run cordova plugin add cordova-plugin-splashscreen@6.0.2
npm run cordova plugin add cordova-plugin-statusbar@4.0.0
npm run cordova plugin add cordova-plugin-whitelist@1.3.5
npm run cordova plugin add cordova-plugin-screen-orientation@3.0.4
npm run cordova plugin add cordova-plugin-firebasex@16.5.0
npm run cordova plugin add cordova-plugin-camera@7.0.0
npm run cordova plugin add cordova-plugin-inappbrowser@6.0.0
npm run cordova plugin add cordova-plugin-androidx@3.0.0
npm run cordova plugin add cordova-plugin-androidx-adapter@1.1.3
npm run cordova plugin add ionic-plugin-keyboard@2.2.1

# Preparazione piattaforme
echo "⚙️ Preparazione piattaforme..."
npm run cordova:prepare

echo "✅ Aggiornamento completato!"
echo ""
echo "📋 Riepilogo aggiornamenti:"
echo "- Android Target SDK: 34 (Android 14)"
echo "- iOS Deployment Target: 15.0"
echo "- Cordova Android: 13.0.0"
echo "- Cordova iOS: 7.1.0"
echo "- Plugin aggiornati alle ultime versioni"
echo ""
echo "🔍 Prossimi passi:"
echo "1. Testare la build: npm run cordova:build android"
echo "2. Testare la build: npm run cordova:build ios"
echo "3. Verificare che tutte le funzionalità funzionino correttamente"
echo ""
echo "⚠️ Note importanti:"
echo "- Verificare che Android Studio sia aggiornato (Jellyfish o superiore)"
echo "- Verificare che Xcode sia aggiornato (15 o superiore)"
echo "- Testare su dispositivi reali prima del deploy"
