image: node:8.6.0

build:
  type: test
  script:
   - npm install

dev_deploy:
  type: deploy
  script:
    - echo "DEVELOP HEROKU SERVER"
    - gem install dpl
    - dpl --provider=heroku --app=$HEROKU_APP_DEVELOP --api-key=$HEROKU_API_KEY
  only:
    - develop

prod_deploy:
  type: deploy
  script:
    - echo "MASTER HEROKU SERVER"
    - gem install dpl
    - dpl --provider=heroku --app=$HEROKU_APP_MASTER --api-key=$HEROKU_API_KEY
  only:
    - master