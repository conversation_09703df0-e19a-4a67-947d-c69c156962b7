@echo off
echo 🚀 Installazione EverHair senza problemi GRPC (Windows)
echo ========================================================

echo 📦 Creazione backup...
copy package.json package.json.backup >nul 2>&1

echo 🛑 Terminazione processi Node.js...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im phantomjs.exe >nul 2>&1
timeout /t 2 >nul

echo 🧹 Pulizia completa...
if exist node_modules (
    echo Rimozione node_modules...
    rmdir /s /q node_modules 2>nul
)

if exist package-lock.json (
    echo Rimozione package-lock.json...
    del package-lock.json
)

echo Pulizia cache npm...
npm cache clean --force

echo 📥 Installazione dipendenze senza GRPC...
npm config set target_platform win32
npm config set target_arch x64
npm config set python python2.7
npm config set msvs_version 2019

echo Installazione con ignore-scripts per evitare problemi di compilazione...
npm install --ignore-scripts

if %errorlevel% neq 0 (
    echo ❌ Errore durante npm install con ignore-scripts
    echo 💡 Proviamo installazione selettiva...
    
    echo Installazione dipendenze core...
    npm install @angular/animations@^4.4.7 --save
    npm install @angular/common@^4.4.7 --save
    npm install @angular/compiler@^4.4.7 --save
    npm install @angular/core@^4.4.7 --save
    npm install @angular/forms@^4.4.7 --save
    npm install @angular/http@^4.4.7 --save
    npm install @angular/platform-browser@^4.4.7 --save
    npm install @angular/platform-browser-dynamic@^4.4.7 --save
    npm install @angular/router@^4.4.7 --save
    
    echo Installazione Ionic...
    npm install ionic-angular@^3.9.10 --save
    npm install @ionic/storage@2.1.3 --save
    npm install @ionic/app-scripts@^3.2.0 --save
    
    echo Installazione Firebase moderno...
    npm install firebase@^9.23.0 --save
    npm install @angular/fire@^7.6.1 --save
    
    echo Installazione altre dipendenze...
    npm install rxjs@^5.5.12 --save
    npm install zone.js@^0.8.29 --save
    npm install core-js@^3.38.1 --save
    npm install lodash@^4.17.21 --save
    npm install ts-md5@^1.3.1 --save
    npm install intl@^1.2.5 --save
    
    echo Installazione Cordova...
    npm install cordova@^12.0.0 --save
    npm install cordova-android@^13.0.0 --save
    npm install cordova-ios@^7.1.0 --save
)

echo 🔧 Installazione tool di sviluppo...
npm install @angular/cli@^1.7.4 --save-dev
npm install @angular/compiler-cli@^4.4.7 --save-dev
npm install typescript@^2.3.4 --save-dev

echo 📱 Ricostruzione script post-install necessari...
npm rebuild node-sass --unsafe-perm

echo ✅ Installazione completata senza GRPC!
echo.
echo 📋 Modifiche apportate:
echo - Firebase aggiornato da 4.8.0 a 9.23.0
echo - AngularFire2 sostituito con @angular/fire 7.6.1
echo - Evitato il pacchetto GRPC problematico
echo - Installazione con ignore-scripts per evitare errori di compilazione
echo.
echo 🔍 Prossimi passi:
echo 1. Verificare che npm start funzioni
echo 2. Aggiornare il codice Firebase se necessario
echo 3. Testare le funzionalità Firebase
echo.
echo ⚠️ Note importanti:
echo - Il codice Firebase potrebbe richiedere piccoli aggiornamenti
echo - La nuova versione di Firebase ha una sintassi leggermente diversa
echo - Consultare la documentazione di migrazione Firebase v9

pause
