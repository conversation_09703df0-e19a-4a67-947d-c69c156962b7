
# EverHair


### iOs
```sh
$ npm run cordova:prepare ios
```

### Install
NODE 16.20.0+ (Raccomandato: 18.x o 20.x)

#### Aggiornamento Compatibilità 2024
Per aggiornare alla compatibilità con Android 14 (SDK 34) e iOS 15+:
```sh
$ chmod +x update-compatibility.sh
$ ./update-compatibility.sh
```

#### Installazione Standard
```sh
$ npm i
$ npm i -D @types/lodash@ts2.3;
$ npm i firebase@4.8.0
$ npm rebuild node-sass
npm install --save-dev  --unsafe-perm node-sass
$ npm start
```

#### REQUISITI AGGIORNATI 2024
```sh
# Android
- Target SDK: 34 (Android 14) - Obbligatorio per Google Play Store
- Min SDK: 24 (Android 7.0)
- Android Studio: Jellyfish o superiore
- Build Tools: 34.0.0

# iOS
- Deployment Target: 15.0
- Xcode: 15.0 o superiore
- iOS SDK: 17.0+
```

#### ERRORI COMUNI
```sh
# Android Build Tools 34.0.0 non trovati
cd ~/Library/Android/sdk/build-tools/34.0.0
# Verificare che d8 e dx siano presenti

# Se mancano, installare tramite Android Studio SDK Manager
# Oppure rinominare da versioni precedenti (solo se necessario):
# mv d8 dx && cd lib && mv d8.jar dx.jar
```

#### TIPS mac
```sh
brew install cocoapods 
 brew cleanup -d -v 

```
