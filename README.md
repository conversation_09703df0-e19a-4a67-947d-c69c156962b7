
# EverHair


### iOs
```sh
$ npm run cordova:prepare ios
```

### Install
NODE 14.17.6
lanciare reset-npm.sh

```sh
$ npm i
$ npm i -D @types/lodash@ts2.3;
$ npm i firebase@4.8.0
$ npm rebuild node-sass
npm install --save-dev  --unsafe-perm node-sass 
$ npm start
```

#### ERRORI
```sh
se dice android build tools not installedcd ~/Library/Android/sdk/build-tools/31.0.0 \
  && mv d8 dx \
  && cd lib  \
  && mv d8.jar dx.jar
   rinominare questi file
quando npm run cordova android da errore che non trova file in build-tools ->  rinominare filae d8.bat e d8.jar in dx.* 
```

#### TIPS mac
```sh
brew install cocoapods 
 brew cleanup -d -v 

```
