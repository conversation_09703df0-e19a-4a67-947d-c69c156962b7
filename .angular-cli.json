{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "project": {"name": "<PERSON><PERSON>r"}, "apps": [{"root": "src", "outDir": "www", "assets": ["assets", "favicon.ico", "manifest.json", "robots.txt"], "index": "index.html", "main": "main.ts", "serviceWorker": true, "polyfills": "polyfills.ts", "test": "test.ts", "tsconfig": "tsconfig.app.json", "testTsconfig": "tsconfig.spec.json", "prefix": "app", "styles": ["main.scss"], "scripts": [], "environmentSource": "environments/environment.ts", "environments": {"dev": "environments/environment.ts", "prod": "environments/environment.prod.ts"}}], "e2e": {"protractor": {"config": "./protractor.conf.js"}}, "lint": [{"project": "src/tsconfig.app.json"}, {"project": "src/tsconfig.spec.json"}, {"project": "e2e/tsconfig.e2e.json"}], "test": {"karma": {"config": "./karma.conf.js"}}, "defaults": {"styleExt": "scss", "component": {}}}