# This is a sample build configuration for JavaScript.
# Check our guides at https://confluence.atlassian.com/x/14UWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: node:6.11.1

clone:
  depth: full
pipelines:
  default:
    - step:
        script:
          - npm install
  branches:
    master:
        - step:
            script:
                - echo "MASTER HEROKU SERVER"
                - git push https://heroku:$<EMAIL>/$HEROKU_APP_MASTER.git HEAD
    develop:
        - step:
            script:
                - echo "DEVELOP HEROKU SERVER"
                - git push https://heroku:$<EMAIL>/$HEROKU_APP_DEVELOP.git HEAD:master